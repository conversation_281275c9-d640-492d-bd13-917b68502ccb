import React, { useState, useEffect } from 'react';

const VirtualKeyboard = ({ isVisible, onKeyPress, onClose, currentText = '' }) => {
  const [isShifted, setIsShifted] = useState(false);
  const [showEmojis, setShowEmojis] = useState(false);

  const normalKeys = [
    ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
    ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'],
    ['z', 'x', 'c', 'v', 'b', 'n', 'm']
  ];

  const shiftedKeys = [
    ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
    ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],
    ['Z', 'X', 'C', 'V', 'B', 'N', 'M']
  ];

  const numberKeys = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];
  
  const emojis = [
    '😊', '😂', '❤️', '👍', '👎', '😍', '😢', '😮', '😡', '🤔',
    '🙏', '👋', '🎉', '🔥', '💯', '✨', '🌟', '💫', '🚀', '💝'
  ];

  const specialKeys = [
    { key: 'space', label: 'space', width: 'flex-1' },
    { key: 'backspace', label: '⌫', width: 'w-16' },
    { key: 'enter', label: 'return', width: 'w-20' }
  ];

  const handleKeyPress = (key) => {
    if (navigator.vibrate) {
      navigator.vibrate(10);
    }

    if (key === 'shift') {
      setIsShifted(!isShifted);
    } else if (key === 'emoji') {
      setShowEmojis(!showEmojis);
    } else if (key === 'space') {
      onKeyPress(' ');
    } else if (key === 'backspace') {
      onKeyPress('BACKSPACE');
    } else if (key === 'enter') {
      onKeyPress('ENTER');
    } else {
      onKeyPress(key);
      if (isShifted) {
        setIsShifted(false); 
      }
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-gradient-to-t from-gray-200 via-gray-150 to-gray-100 shadow-2xl border-t border-gray-300 max-h-[50vh] overflow-hidden">
      {/* Enhanced Header */}
      <div className="flex items-center justify-between px-4 py-2.5 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200 shadow-sm">
        <div className="flex items-center gap-3">
          <div className="w-1 h-4 bg-gradient-to-b from-blue-500 to-blue-600 rounded-full shadow-sm"></div>
          <span className="text-sm text-gray-700 font-semibold tracking-wide">Virtual Keyboard</span>
        </div>
        <button
          onClick={onClose}
          className="p-2 rounded-full bg-gray-200 hover:bg-gray-300 active:bg-gray-400 transition-all duration-150 active:scale-95 shadow-sm border border-gray-300"
        >
          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div>

      {/* Enhanced Keyboard Content */}
      <div className="p-3 bg-gradient-to-b from-gray-100 to-gray-150">
        {showEmojis ? (
          /* Enhanced Emoji Panel */
          <div className="grid grid-cols-8 gap-2 mb-3 p-3 bg-white rounded-xl shadow-sm border border-gray-200">
            {emojis.map((emoji, index) => (
              <button
                key={index}
                onClick={() => handleKeyPress(emoji)}
                className="aspect-square flex items-center justify-center text-xl bg-gray-50 hover:bg-blue-50 active:bg-blue-100 rounded-lg transition-all duration-150 active:scale-95 shadow-sm border border-gray-200 hover:border-blue-200"
              >
                {emoji}
              </button>
            ))}
          </div>
        ) : (
          /* Enhanced Keyboard Layout */
          <div className="space-y-2">
            {/* Number Row */}
            <div className="flex gap-1.5">
              {numberKeys.map((key) => (
                <button
                  key={key}
                  onClick={() => handleKeyPress(key)}
                  className="flex-1 h-10 bg-white hover:bg-gray-100 active:bg-gray-200 rounded-lg text-gray-900 font-semibold transition-all duration-150 shadow-sm border border-gray-300 active:scale-95 text-base"
                >
                  {key}
                </button>
              ))}
            </div>

            {/* Letter Rows */}
            {(isShifted ? shiftedKeys : normalKeys).map((row, rowIndex) => (
              <div key={rowIndex} className={`flex gap-1.5 ${rowIndex === 1 ? 'px-3' : rowIndex === 2 ? 'px-6' : ''}`}>
                {row.map((key) => (
                  <button
                    key={key}
                    onClick={() => handleKeyPress(key)}
                    className="flex-1 h-12 bg-white hover:bg-gray-100 active:bg-gray-200 rounded-lg text-gray-900 font-semibold transition-all duration-150 shadow-sm border border-gray-300 active:scale-95 text-base"
                  >
                    {key}
                  </button>
                ))}
              </div>
            ))}

            {/* Bottom Row - Special Keys */}
            <div className="flex gap-1.5 items-center">
              {/* Shift Key */}
              <button
                onClick={() => handleKeyPress('shift')}
                className={`w-14 h-12 rounded-lg font-bold transition-all duration-150 shadow-sm border active:scale-95 text-base ${
                  isShifted
                    ? 'bg-blue-500 hover:bg-blue-600 text-white border-blue-600 shadow-blue-200'
                    : 'bg-gray-300 hover:bg-gray-400 active:bg-gray-500 text-gray-800 border-gray-400'
                }`}
              >
                ⇧
              </button>

              {/* Special Keys */}
              {specialKeys.map((special) => (
                <button
                  key={special.key}
                  onClick={() => handleKeyPress(special.key)}
                  className={`${special.width} h-12 rounded-lg font-semibold transition-all duration-150 shadow-sm border active:scale-95 text-sm ${
                    special.key === 'enter'
                      ? 'bg-blue-500 hover:bg-blue-600 active:bg-blue-700 text-white border-blue-600 shadow-blue-200'
                      : special.key === 'space'
                      ? 'bg-white hover:bg-gray-100 active:bg-gray-200 text-gray-600 border-gray-300'
                      : 'bg-gray-300 hover:bg-gray-400 active:bg-gray-500 text-gray-800 border-gray-400'
                  }`}
                >
                  {special.key === 'backspace' ? '⌫' : special.key === 'space' ? '' : special.label}
                </button>
              ))}

              {/* Emoji Toggle */}
              <button
                onClick={() => handleKeyPress('emoji')}
                className={`w-14 h-12 rounded-lg font-medium transition-all duration-150 shadow-sm border active:scale-95 text-lg ${
                  showEmojis
                    ? 'bg-orange-400 hover:bg-orange-500 text-white border-orange-500 shadow-orange-200'
                    : 'bg-gray-300 hover:bg-gray-400 active:bg-gray-500 text-gray-800 border-gray-400'
                }`}
              >
                😊
              </button>
            </div>
          </div>
        )}

        {/* Enhanced Footer */}
        <div className="flex justify-center mt-3">
          <button
            onClick={() => setShowEmojis(!showEmojis)}
            className="px-8 py-2.5 bg-gradient-to-r from-gray-200 to-gray-300 hover:from-gray-300 hover:to-gray-400 active:from-gray-400 active:to-gray-500 rounded-full text-gray-700 text-sm font-semibold transition-all duration-150 shadow-md border border-gray-300 active:scale-95"
          >
            {showEmojis ? '🔤 ABC' : '😊 Emojis'}
          </button>
        </div>

        {/* iOS-style Home Indicator */}
        <div className="flex justify-center mt-3 mb-2">
          <div className="w-36 h-1.5 bg-gray-400 rounded-full shadow-sm"></div>
        </div>
      </div>
    </div>
  );
};

export default VirtualKeyboard;
