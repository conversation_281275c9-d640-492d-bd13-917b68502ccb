import React, { useState, useEffect } from 'react';

const VirtualKeyboard = ({ isVisible, onKeyPress, onClose, currentText = '' }) => {
  const [isShifted, setIsShifted] = useState(false);
  const [showEmojis, setShowEmojis] = useState(false);

  const normalKeys = [
    ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
    ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'],
    ['z', 'x', 'c', 'v', 'b', 'n', 'm']
  ];

  const shiftedKeys = [
    ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
    ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],
    ['Z', 'X', 'C', 'V', 'B', 'N', 'M']
  ];

  const numberKeys = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];
  
  const emojis = [
    '😊', '😂', '❤️', '👍', '👎', '😍', '😢', '😮', '😡', '🤔',
    '🙏', '👋', '🎉', '🔥', '💯', '✨', '🌟', '💫', '🚀', '💝'
  ];

  const specialKeys = [
    { key: 'space', label: 'space', width: 'flex-1' },
    { key: 'backspace', label: '⌫', width: 'w-16' },
    { key: 'enter', label: 'return', width: 'w-20' }
  ];

  const handleKeyPress = (key) => {
    if (navigator.vibrate) {
      navigator.vibrate(10);
    }

    if (key === 'shift') {
      setIsShifted(!isShifted);
    } else if (key === 'emoji') {
      setShowEmojis(!showEmojis);
    } else if (key === 'space') {
      onKeyPress(' ');
    } else if (key === 'backspace') {
      onKeyPress('BACKSPACE');
    } else if (key === 'enter') {
      onKeyPress('ENTER');
    } else {
      onKeyPress(key);
      if (isShifted) {
        setIsShifted(false); 
      }
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-gray-100 border-t border-gray-300 shadow-2xl">
      <div className="flex items-center justify-between px-4 py-2 bg-gray-200 border-b border-gray-300">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600 font-medium">Keyboard</span>
        </div>
        <button
          onClick={onClose}
          className="p-2 rounded-md bg-gray-300 hover:bg-gray-400 transition-colors active:scale-95"
        >
          <svg className="w-4 h-4 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div>

      <div className="p-2 bg-gray-100">
        {showEmojis ? (
          <div className="grid grid-cols-8 gap-1 mb-3 p-2 bg-white rounded-lg">
            {emojis.map((emoji, index) => (
              <button
                key={index}
                onClick={() => handleKeyPress(emoji)}
                className="aspect-square flex items-center justify-center text-xl bg-gray-50 hover:bg-gray-200 rounded-md transition-all duration-150 active:scale-95 active:bg-gray-300"
              >
                {emoji}
              </button>
            ))}
          </div>
        ) : (
          <div className="space-y-1">
            <div className="flex gap-1">
              {numberKeys.map((key) => (
                <button
                  key={key}
                  onClick={() => handleKeyPress(key)}
                  className="flex-1 h-10 bg-white hover:bg-gray-200 active:bg-gray-300 rounded-md text-gray-900 font-medium transition-all duration-150 shadow-sm border border-gray-200 active:scale-95"
                >
                  {key}
                </button>
              ))}
            </div>

            {(isShifted ? shiftedKeys : normalKeys).map((row, rowIndex) => (
              <div key={rowIndex} className={`flex gap-1 ${rowIndex === 1 ? 'px-4' : rowIndex === 2 ? 'px-8' : ''}`}>
                {row.map((key) => (
                  <button
                    key={key}
                    onClick={() => handleKeyPress(key)}
                    className="flex-1 h-11 bg-white hover:bg-gray-200 active:bg-gray-300 rounded-md text-gray-900 font-medium transition-all duration-150 shadow-sm border border-gray-200 active:scale-95"
                  >
                    {key}
                  </button>
                ))}
              </div>
            ))}

            <div className="flex gap-1 items-center">

              <button
                onClick={() => handleKeyPress('shift')}
                className={`w-12 h-11 rounded-md font-medium transition-all duration-150 shadow-sm border active:scale-95 ${
                  isShifted
                    ? 'bg-blue-500 text-white border-blue-600'
                    : 'bg-gray-300 hover:bg-gray-400 active:bg-gray-500 text-gray-800 border-gray-400'
                }`}
              >
                ⇧
              </button>

              {specialKeys.map((special) => (
                <button
                  key={special.key}
                  onClick={() => handleKeyPress(special.key)}
                  className={`${special.width} h-11 rounded-md font-medium transition-all duration-150 shadow-sm border active:scale-95 ${
                    special.key === 'enter'
                      ? 'bg-blue-500 hover:bg-blue-600 active:bg-blue-700 text-white border-blue-600'
                      : special.key === 'space'
                      ? 'bg-white hover:bg-gray-200 active:bg-gray-300 text-gray-900 border-gray-200'
                      : 'bg-gray-300 hover:bg-gray-400 active:bg-gray-500 text-gray-800 border-gray-400'
                  }`}
                >
                  {special.key === 'backspace' ? '⌫' : special.label}
                </button>
              ))}

              <button
                onClick={() => handleKeyPress('emoji')}
                className={`w-12 h-11 rounded-md font-medium transition-all duration-150 shadow-sm border active:scale-95 ${
                  showEmojis
                    ? 'bg-orange-400 text-white border-orange-500'
                    : 'bg-gray-300 hover:bg-gray-400 active:bg-gray-500 text-gray-800 border-gray-400'
                }`}
              >
                😊
              </button>
            </div>
          </div>
        )}

        <div className="flex justify-center mt-2">
          <button
            onClick={() => setShowEmojis(!showEmojis)}
            className="px-6 py-2 bg-gray-200 hover:bg-gray-300 active:bg-gray-400 rounded-full text-gray-700 text-sm font-medium transition-all duration-150 shadow-sm border border-gray-300 active:scale-95"
          >
            {showEmojis ? 'ABC' : '😊 Emojis'}
          </button>
        </div>

        <div className="flex justify-center mt-2 mb-1">
          <div className="w-32 h-1 bg-gray-400 rounded-full"></div>
        </div>
      </div>
    </div>
  );
};

export default VirtualKeyboard;
