import React, { useState, useEffect } from 'react';

const VirtualKeyboard = ({ isVisible, onKeyPress, onClose, currentText = '' }) => {
  const [isShifted, setIsShifted] = useState(false);
  const [showEmojis, setShowEmojis] = useState(false);

  const normalKeys = [
    ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
    ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'],
    ['z', 'x', 'c', 'v', 'b', 'n', 'm']
  ];

  const shiftedKeys = [
    ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
    ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],
    ['Z', 'X', 'C', 'V', 'B', 'N', 'M']
  ];

  const numberKeys = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];
  
  const emojis = [
    '😊', '😂', '❤️', '👍', '👎', '😍', '😢', '😮', '😡', '🤔',
    '🙏', '👋', '🎉', '🔥', '💯', '✨', '🌟', '💫', '🚀', '💝'
  ];

  const specialKeys = [
    { key: 'space', label: 'Space', width: 'flex-1' },
    { key: 'backspace', label: '⌫', width: 'w-16' },
    { key: 'enter', label: 'Send', width: 'w-20' }
  ];

  const handleKeyPress = (key) => {
    // Add haptic feedback for mobile
    if (navigator.vibrate) {
      navigator.vibrate(10);
    }

    if (key === 'shift') {
      setIsShifted(!isShifted);
    } else if (key === 'emoji') {
      setShowEmojis(!showEmojis);
    } else if (key === 'space') {
      onKeyPress(' ');
    } else if (key === 'backspace') {
      onKeyPress('BACKSPACE');
    } else if (key === 'enter') {
      onKeyPress('ENTER');
    } else {
      onKeyPress(key);
      if (isShifted) {
        setIsShifted(false); // Auto-disable shift after typing
      }
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-gradient-to-t from-slate-900 via-slate-800 to-slate-900/95 backdrop-blur-xl border-t border-slate-700/50 shadow-2xl">
      {/* Keyboard Header */}
      <div className="flex items-center justify-between p-3 border-b border-slate-700/30">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-sm text-slate-300 font-medium">Virtual Keyboard</span>
        </div>
        <button
          onClick={onClose}
          className="p-2 rounded-lg bg-slate-700/50 hover:bg-slate-600/50 transition-colors"
        >
          <svg className="w-4 h-4 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Keyboard Content */}
      <div className="p-3">
        {showEmojis ? (
          /* Emoji Panel */
          <div className="grid grid-cols-10 gap-2 mb-4">
            {emojis.map((emoji, index) => (
              <button
                key={index}
                onClick={() => handleKeyPress(emoji)}
                className="aspect-square flex items-center justify-center text-lg bg-slate-700/50 hover:bg-slate-600/50 rounded-lg transition-all duration-200 hover:scale-110 active:scale-95"
              >
                {emoji}
              </button>
            ))}
          </div>
        ) : (
          /* Regular Keyboard */
          <div className="space-y-2">
            {/* Number Row */}
            <div className="flex gap-1 justify-center">
              {numberKeys.map((key) => (
                <button
                  key={key}
                  onClick={() => handleKeyPress(key)}
                  className="flex-1 h-10 bg-slate-700/70 hover:bg-slate-600/70 rounded-lg text-white font-medium transition-all duration-200 hover:scale-105 active:scale-95 shadow-lg"
                >
                  {key}
                </button>
              ))}
            </div>

            {/* Letter Rows */}
            {(isShifted ? shiftedKeys : normalKeys).map((row, rowIndex) => (
              <div key={rowIndex} className="flex gap-1 justify-center">
                {row.map((key) => (
                  <button
                    key={key}
                    onClick={() => handleKeyPress(key)}
                    className="flex-1 h-12 bg-slate-700/70 hover:bg-slate-600/70 rounded-lg text-white font-medium transition-all duration-200 hover:scale-105 active:scale-95 shadow-lg"
                  >
                    {key}
                  </button>
                ))}
              </div>
            ))}

            {/* Bottom Row with Special Keys */}
            <div className="flex gap-1 items-center">
              {/* Shift Key */}
              <button
                onClick={() => handleKeyPress('shift')}
                className={`w-16 h-12 rounded-lg font-medium transition-all duration-200 hover:scale-105 active:scale-95 shadow-lg ${
                  isShifted 
                    ? 'bg-cyan-500 text-white' 
                    : 'bg-slate-700/70 hover:bg-slate-600/70 text-white'
                }`}
              >
                ⇧
              </button>

              {/* Space and other keys */}
              {specialKeys.map((special) => (
                <button
                  key={special.key}
                  onClick={() => handleKeyPress(special.key)}
                  className={`${special.width} h-12 bg-slate-700/70 hover:bg-slate-600/70 rounded-lg text-white font-medium transition-all duration-200 hover:scale-105 active:scale-95 shadow-lg ${
                    special.key === 'enter' ? 'bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700' : ''
                  }`}
                >
                  {special.label}
                </button>
              ))}

              {/* Emoji Toggle */}
              <button
                onClick={() => handleKeyPress('emoji')}
                className={`w-16 h-12 rounded-lg font-medium transition-all duration-200 hover:scale-105 active:scale-95 shadow-lg ${
                  showEmojis 
                    ? 'bg-pink-500 text-white' 
                    : 'bg-slate-700/70 hover:bg-slate-600/70 text-white'
                }`}
              >
                😊
              </button>
            </div>
          </div>
        )}

        {/* Toggle Button */}
        <div className="flex justify-center mt-3">
          <button
            onClick={() => setShowEmojis(!showEmojis)}
            className="px-4 py-2 bg-slate-700/50 hover:bg-slate-600/50 rounded-lg text-slate-300 text-sm font-medium transition-colors"
          >
            {showEmojis ? 'ABC' : '😊'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default VirtualKeyboard;
