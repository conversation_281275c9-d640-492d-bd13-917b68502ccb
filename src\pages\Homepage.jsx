import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { female<PERSON>ersona, malePersona } from '../personas';

const HomePage = () => {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <div className="min-h-full w-full flex flex-col justify-center items-center p-4 sm:p-6 md:p-8 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 overflow-y-auto">
      {/* Simplified Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-950/20 via-purple-950/20 to-pink-950/20"></div>

      {/* Subtle floating elements - fewer for mobile */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-gradient-to-r from-cyan-400/30 to-pink-400/30 rounded-full animate-pulse hidden sm:block"
            style={{
              left: `${20 + Math.random() * 60}%`,
              top: `${20 + Math.random() * 60}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${4 + Math.random() * 2}s`
            }}
          ></div>
        ))}
      </div>

      {/* Header Section */}
      <div className={`relative z-10 text-center mb-8 sm:mb-12 md:mb-16 px-4 transition-all duration-1000 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
        <div className="mb-4">
          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 mb-2">
            AI Dost
          </h1>
          <div className="w-16 sm:w-20 h-1 bg-gradient-to-r from-cyan-400 to-purple-400 rounded-full mx-auto"></div>
        </div>

        <p className="text-slate-300 text-base sm:text-lg md:text-xl max-w-lg mx-auto leading-relaxed mb-6">
          Your friendly AI companion, ready to chat anytime. Choose who you'd like to talk with!
        </p>

        {/* Features */}
        <div className="flex flex-wrap justify-center gap-3 sm:gap-4 text-xs sm:text-sm text-slate-400 mb-8">
          <div className="flex items-center gap-2 bg-slate-800/50 px-3 py-2 rounded-full">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>Always Online</span>
          </div>
          <div className="flex items-center gap-2 bg-slate-800/50 px-3 py-2 rounded-full">
            <svg className="w-3 h-3 text-cyan-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <span>Smart & Helpful</span>
          </div>
          <div className="flex items-center gap-2 bg-slate-800/50 px-3 py-2 rounded-full">
            <svg className="w-3 h-3 text-pink-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
            </svg>
            <span>Friendly Chat</span>
          </div>
        </div>
      </div>

      {/* Persona Cards */}
      <div className={`relative z-10 w-full max-w-4xl transition-all duration-1000 delay-300 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8 px-4">
          <PersonaCard persona={femalePersona} gender="female" index={0} />
          <PersonaCard persona={malePersona} gender="male" index={1} />
        </div>

        {/* OR divider for mobile */}
        <div className="flex items-center justify-center my-6 md:hidden">
          <div className="flex-1 h-px bg-slate-700"></div>
          <div className="px-4 text-slate-400 text-sm font-medium">OR</div>
          <div className="flex-1 h-px bg-slate-700"></div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-8 sm:mt-12 text-center text-xs sm:text-sm text-slate-500">
        <p>Start chatting in seconds • No signup required • Free to use</p>
      </div>
    </div>
  );
};

const PersonaCard = ({ persona, gender, index }) => {
  const [isPressed, setIsPressed] = useState(false);

  return (
    <Link
      to={`/chat/${gender}`}
      className="group relative block w-full"
      onTouchStart={() => setIsPressed(true)}
      onTouchEnd={() => setIsPressed(false)}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onMouseLeave={() => setIsPressed(false)}
      style={{ animationDelay: `${index * 150}ms` }}
    >
      {/* Card Container */}
      <div className={`relative bg-gradient-to-br from-slate-800/90 to-slate-900/90 rounded-2xl border border-slate-700/50 p-6 sm:p-8 transition-all duration-300 backdrop-blur-sm ${
        isPressed
          ? 'scale-95 shadow-lg'
          : 'hover:border-slate-600/70 hover:shadow-xl hover:scale-[1.02] active:scale-95'
      }`}>

        {/* Status Indicator */}
        <div className="absolute top-4 right-4 flex items-center gap-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-xs text-green-400 font-medium hidden sm:inline">Online</span>
        </div>

        {/* Avatar */}
        <div className="flex flex-col items-center mb-6">
          <div className="relative mb-4">
            <img
              src={persona.avatar}
              alt={persona.name}
              className="w-20 h-20 sm:w-24 sm:h-24 rounded-full object-cover border-3 border-slate-600 shadow-lg"
            />
            {/* Online badge */}
            <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-400 rounded-full border-2 border-slate-800 flex items-center justify-center">
              <svg className="w-3 h-3 text-slate-800" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
          </div>

          {/* Name */}
          <h2 className={`text-2xl sm:text-3xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r ${
            gender === 'female' ? 'from-pink-400 to-purple-400' : 'from-blue-400 to-cyan-400'
          }`}>
            {persona.name}
          </h2>

          {/* Tagline */}
          <p className="text-slate-300 text-sm sm:text-base text-center leading-relaxed mb-6 min-h-[2.5rem] flex items-center">
            {persona.tagline}
          </p>
        </div>

        {/* Features */}
        <div className="space-y-3 mb-6">
          <div className="flex items-center gap-3 text-slate-300 text-sm">
            <div className={`w-2 h-2 rounded-full ${gender === 'female' ? 'bg-pink-400' : 'bg-cyan-400'}`}></div>
            <span>Friendly & Understanding</span>
          </div>
          <div className="flex items-center gap-3 text-slate-300 text-sm">
            <div className={`w-2 h-2 rounded-full ${gender === 'female' ? 'bg-purple-400' : 'bg-blue-400'}`}></div>
            <span>Always Ready to Listen</span>
          </div>
          <div className="flex items-center gap-3 text-slate-300 text-sm">
            <div className={`w-2 h-2 rounded-full ${gender === 'female' ? 'bg-pink-300' : 'bg-cyan-300'}`}></div>
            <span>Speaks Hinglish</span>
          </div>
        </div>

        {/* Action Button */}
        <button className={`w-full py-3 sm:py-4 px-6 rounded-xl font-semibold text-base sm:text-lg text-center transition-all duration-300 ${
          gender === 'female'
            ? 'bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white shadow-lg shadow-pink-500/25'
            : 'bg-gradient-to-r from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700 text-white shadow-lg shadow-blue-500/25'
        } ${isPressed ? 'scale-95' : 'hover:scale-105 active:scale-95'}`}>
          <span className="flex items-center justify-center gap-2">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            Start Chatting with {persona.name}
          </span>
        </button>
      </div>
    </Link>
  );
};

export default HomePage;