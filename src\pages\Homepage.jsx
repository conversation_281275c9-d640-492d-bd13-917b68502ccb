import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { female<PERSON>ersona, malePersona } from '../personas';

const HomePage = () => {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <div className="h-screen w-full flex flex-col justify-center items-center p-3 sm:p-4 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 overflow-hidden">
      {/* Simplified Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-950/20 via-purple-950/20 to-pink-950/20"></div>

      {/* Header Section - Compact */}
      <div className={`relative z-10 text-center mb-3 sm:mb-4 px-4 transition-all duration-1000 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
        <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 mb-2">
          AI Dost
        </h1>
        <p className="text-slate-300 text-sm sm:text-base max-w-md mx-auto leading-relaxed mb-2">
          Choose your AI companion for friendly conversations
        </p>

        {/* Compact Features */}
        <div className="flex justify-center gap-2 text-xs text-slate-400">
          <span className="flex items-center gap-1">
            <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
            Online 24/7
          </span>
          <span className="text-slate-600">•</span>
          <span>Smart & Friendly</span>
          <span className="text-slate-600">•</span>
          <span>Free to Use</span>
        </div>
      </div>

      {/* Persona Cards - Compact */}
      <div className={`relative z-10 w-full max-w-3xl flex-1 flex items-center justify-center transition-all duration-1000 delay-300 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 px-4 w-full">
          <PersonaCard persona={femalePersona} gender="female" index={0} />
          <PersonaCard persona={malePersona} gender="male" index={1} />
        </div>
      </div>
    </div>
  );
};

const PersonaCard = ({ persona, gender, index }) => {
  const [isPressed, setIsPressed] = useState(false);

  return (
    <Link
      to={`/chat/${gender}`}
      className="group relative block w-full"
      onTouchStart={() => setIsPressed(true)}
      onTouchEnd={() => setIsPressed(false)}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onMouseLeave={() => setIsPressed(false)}
      style={{ animationDelay: `${index * 150}ms` }}
    >
      {/* Compact Card Container */}
      <div className={`relative bg-gradient-to-br from-slate-800/90 to-slate-900/90 rounded-xl border border-slate-700/50 p-4 sm:p-5 transition-all duration-300 backdrop-blur-sm ${
        isPressed
          ? 'scale-95 shadow-lg'
          : 'hover:border-slate-600/70 hover:shadow-xl hover:scale-[1.02] active:scale-95'
      }`}>

        {/* Status Indicator */}
        <div className="absolute top-3 right-3 w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>

        {/* Avatar & Info */}
        <div className="flex flex-col items-center text-center mb-4">
          <div className="relative mb-3">
            <img
              src={persona.avatar}
              alt={persona.name}
              className="w-16 h-16 sm:w-18 sm:h-18 rounded-full object-cover border-2 border-slate-600 shadow-lg"
            />
          </div>

          {/* Name */}
          <h2 className={`text-xl sm:text-2xl font-bold mb-1 bg-clip-text text-transparent bg-gradient-to-r ${
            gender === 'female' ? 'from-pink-400 to-purple-400' : 'from-blue-400 to-cyan-400'
          }`}>
            {persona.name}
          </h2>

          {/* Tagline */}
          <p className="text-slate-300 text-xs sm:text-sm leading-relaxed mb-3">
            {persona.tagline}
          </p>
        </div>

        {/* Compact Features */}
        <div className="flex justify-center gap-4 mb-4 text-xs text-slate-400">
          <span className="flex items-center gap-1">
            <div className={`w-1.5 h-1.5 rounded-full ${gender === 'female' ? 'bg-pink-400' : 'bg-cyan-400'}`}></div>
            Friendly
          </span>
          <span className="flex items-center gap-1">
            <div className={`w-1.5 h-1.5 rounded-full ${gender === 'female' ? 'bg-purple-400' : 'bg-blue-400'}`}></div>
            Helpful
          </span>
          <span className="flex items-center gap-1">
            <div className={`w-1.5 h-1.5 rounded-full ${gender === 'female' ? 'bg-pink-300' : 'bg-cyan-300'}`}></div>
            Hinglish
          </span>
        </div>

        {/* Action Button */}
        <button className={`w-full py-2.5 sm:py-3 px-4 rounded-lg font-semibold text-sm sm:text-base text-center transition-all duration-300 ${
          gender === 'female'
            ? 'bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white shadow-lg shadow-pink-500/25'
            : 'bg-gradient-to-r from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700 text-white shadow-lg shadow-blue-500/25'
        } ${isPressed ? 'scale-95' : 'hover:scale-105 active:scale-95'}`}>
          <span className="flex items-center justify-center gap-2">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            Chat with {persona.name}
          </span>
        </button>
      </div>
    </Link>
  );
};

export default HomePage;