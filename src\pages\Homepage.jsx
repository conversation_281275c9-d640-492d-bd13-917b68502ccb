import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { female<PERSON>ersona, malePersona } from '../personas';

const HomePage = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
    const handleMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="relative h-full w-full flex flex-col justify-center items-center p-4 md:p-8 overflow-hidden">
      {/* Animated Background Layers */}
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-950 via-purple-950 to-slate-950"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-cyan-950/30 via-transparent to-pink-950/30"></div>

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-cyan-400/20 rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 4}s`
            }}
          ></div>
        ))}
      </div>

      {/* Interactive Glow Effect */}
      <div
        className="absolute w-96 h-96 bg-gradient-radial from-cyan-500/10 via-purple-500/5 to-transparent rounded-full blur-3xl transition-all duration-300 pointer-events-none"
        style={{
          left: mousePosition.x - 192,
          top: mousePosition.y - 192,
        }}
      ></div>

      {/* Main Content */}
      <div className={`relative z-10 text-center mb-10 md:mb-16 transition-all duration-1000 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
        <div className="relative">
          <h1 className="text-6xl sm:text-7xl md:text-8xl lg:text-9xl font-black bg-clip-text text-transparent bg-gradient-to-r from-cyan-300 via-purple-400 to-pink-400 pb-4 animate-gradient-x">
            AI Dost
          </h1>
          <div className="absolute inset-0 text-6xl sm:text-7xl md:text-8xl lg:text-9xl font-black text-cyan-400/10 blur-sm animate-pulse">
            AI Dost
          </div>
        </div>
        <p className="text-slate-300 text-lg sm:text-xl md:text-2xl mt-6 max-w-2xl mx-auto leading-relaxed font-light">
          Choose your AI companion. Someone who truly listens, understands, and is available 24/7.
        </p>
        <div className="flex justify-center mt-6">
          <div className="w-24 h-1 bg-gradient-to-r from-cyan-400 to-pink-400 rounded-full"></div>
        </div>
      </div>

      <div className={`relative z-10 flex flex-col lg:flex-row items-center gap-16 lg:gap-20 transition-all duration-1000 delay-300 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
        <PersonaCard persona={femalePersona} gender="female" index={0} />
        <div className="relative lg:hidden">
          <div className="text-slate-400 font-bold text-xl px-8 py-3 bg-slate-800/30 rounded-full border border-slate-700/50 backdrop-blur-sm">
            OR
          </div>
          <div className="absolute inset-0 bg-gradient-to-r from-cyan-400/20 to-pink-400/20 rounded-full blur-xl"></div>
        </div>
        <PersonaCard persona={malePersona} gender="male" index={1} />
      </div>
    </div>
  );
};

const PersonaCard = ({ persona, gender, index }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Link
      to={`/chat/${gender}`}
      className="group relative block"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ animationDelay: `${index * 200}ms` }}
    >
      {/* Glow Effect Background */}
      <div className={`absolute inset-0 bg-gradient-to-r ${gender === 'female' ? 'from-pink-500/20 to-purple-500/20' : 'from-blue-500/20 to-cyan-500/20'} rounded-3xl blur-xl transition-all duration-500 ${isHovered ? 'scale-110 opacity-100' : 'scale-100 opacity-0'}`}></div>

      {/* Main Card */}
      <div className={`relative w-80 sm:w-96 flex flex-col items-center p-8 bg-gradient-to-br from-slate-800/80 to-slate-900/80 rounded-3xl border transition-all duration-500 backdrop-blur-xl transform-gpu ${
        isHovered
          ? `border-${gender === 'female' ? 'pink' : 'cyan'}-400/60 scale-105 shadow-2xl shadow-${gender === 'female' ? 'pink' : 'cyan'}-500/25`
          : 'border-slate-700/50 hover:border-slate-600/70'
      }`}>

        {/* Avatar Container */}
        <div className="relative mb-6">
          <div className={`absolute inset-0 bg-gradient-to-r ${gender === 'female' ? 'from-pink-400 to-purple-400' : 'from-blue-400 to-cyan-400'} rounded-full blur-md transition-all duration-500 ${isHovered ? 'scale-110 opacity-60' : 'scale-100 opacity-0'}`}></div>
          <img
            src={persona.avatar}
            alt={persona.name}
            className={`relative w-44 h-44 rounded-full border-4 object-cover transition-all duration-500 ${
              isHovered
                ? `border-${gender === 'female' ? 'pink' : 'cyan'}-400 shadow-lg shadow-${gender === 'female' ? 'pink' : 'cyan'}-400/50`
                : 'border-slate-600'
            }`}
          />
          {/* Avatar Ring Animation */}
          <div className={`absolute inset-0 rounded-full border-2 ${gender === 'female' ? 'border-pink-400' : 'border-cyan-400'} transition-all duration-1000 ${isHovered ? 'scale-125 opacity-0' : 'scale-100 opacity-0'}`}></div>
        </div>

        {/* Content */}
        <div className="text-center mb-6">
          <h2 className={`text-4xl font-bold mb-3 bg-clip-text text-transparent bg-gradient-to-r ${gender === 'female' ? 'from-pink-300 to-purple-300' : 'from-blue-300 to-cyan-300'} transition-all duration-300`}>
            {persona.name}
          </h2>
          <p className="text-slate-300 text-lg leading-relaxed px-2 min-h-[3rem] flex items-center justify-center">
            {persona.tagline}
          </p>
        </div>

        {/* Action Button */}
        <div className={`w-full py-4 px-6 rounded-2xl font-bold text-lg text-center transition-all duration-300 ${
          isHovered
            ? `bg-gradient-to-r ${gender === 'female' ? 'from-pink-500 to-purple-500' : 'from-blue-500 to-cyan-500'} text-white shadow-lg transform scale-105`
            : 'bg-slate-700/80 text-slate-200 hover:bg-slate-600/80'
        }`}>
          <span className="flex items-center justify-center gap-2">
            Start Chatting
            <svg className={`w-5 h-5 transition-transform duration-300 ${isHovered ? 'translate-x-1' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </span>
        </div>

        {/* Decorative Elements */}
        <div className={`absolute top-4 right-4 w-3 h-3 ${gender === 'female' ? 'bg-pink-400' : 'bg-cyan-400'} rounded-full transition-all duration-500 ${isHovered ? 'scale-150 opacity-100' : 'scale-100 opacity-40'}`}></div>
        <div className={`absolute bottom-4 left-4 w-2 h-2 ${gender === 'female' ? 'bg-purple-400' : 'bg-blue-400'} rounded-full transition-all duration-700 ${isHovered ? 'scale-150 opacity-100' : 'scale-100 opacity-30'}`}></div>
      </div>
    </Link>
  );
};

export default HomePage;