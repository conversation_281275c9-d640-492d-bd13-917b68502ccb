import React, { useState, useEffect, useRef } from 'react';
import { useParams, Link } from 'react-router-dom';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { femalePersona, malePersona } from '../personas';

const API_KEY = "AIzaSyCrNTLrWGxffTfN5XZAzaQ_HvfYZ8zHDq0";

const genAI = new GoogleGenerativeAI(API_KEY);

const ChatPage = () => {
  const { gender } = useParams();
  const persona = gender === 'female' ? femalePersona : malePersona;

  const [chatHistory, setChatHistory] = useState([]);
  const [userInput, setUserInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [messageCount, setMessageCount] = useState(0);

  const chatModelRef = useRef(null);
  const chatEndRef = useRef(null);
  const inputRef = useRef(null);

  useEffect(() => {
    const initializeChat = () => {
      const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash", systemInstruction: persona.systemInstruction });
      chatModelRef.current = model.startChat({ history: [] });
      setChatHistory([{
        role: 'model',
        parts: [{ text: `Hey! Main ${persona.name} hoon. Let's talk!` }],
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      }]);
    };
    initializeChat();
  }, [gender, persona.name, persona.systemInstruction]);

  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatHistory, isLoading]);

  const sendMessage = async (e) => {
    e.preventDefault();
    if (!userInput.trim() || isLoading) return;

    const userMessageText = userInput;
    const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

    setIsLoading(true);
    setIsTyping(true);
    setUserInput('');
    setMessageCount(prev => prev + 1);

    // Add user message with animation
    setChatHistory(prev => [
      ...prev,
      { role: 'user', parts: [{ text: userMessageText }], timestamp, id: Date.now() }
    ]);

    // Add empty bot message for typing effect
    setTimeout(() => {
      setChatHistory(prev => [
        ...prev,
        { role: 'model', parts: [{ text: '' }], timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }), id: Date.now() + 1, isTyping: true }
      ]);
    }, 300);

    try {
      const result = await chatModelRef.current.sendMessageStream(userMessageText);
      let fullText = "";
      let isFirstChunk = true;

      for await (const chunk of result.stream) {
        const chunkText = chunk.text();
        fullText += chunkText;

        if (isFirstChunk) {
          setIsTyping(false);
          isFirstChunk = false;
        }

        setChatHistory(currentHistory => {
          const newHistory = [...currentHistory];
          const lastMessage = newHistory[newHistory.length - 1];
          if (lastMessage && lastMessage.role === 'model') {
            lastMessage.parts[0].text = fullText;
            lastMessage.isTyping = false;
          }
          return newHistory;
        });

        // Small delay for smooth typing effect
        await new Promise(resolve => setTimeout(resolve, 20));
      }
    } catch (error) {
      console.error("Error sending message:", error);
      setIsTyping(false);
      setChatHistory(currentHistory => {
        const newHistory = [...currentHistory];
        const lastMessage = newHistory[newHistory.length - 1];
        if (lastMessage && lastMessage.role === 'model') {
          lastMessage.parts[0].text = "Oops! Kuch problem ho gayi. Please try again. 😅";
          lastMessage.isTyping = false;
        }
        return newHistory;
      });
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  };

  return (
    <div className="flex flex-col h-full w-full max-w-5xl mx-auto bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 border-x border-slate-700/30 shadow-2xl">
      {/* Enhanced Header */}
      <header className="flex items-center p-4 sm:p-6 border-b border-slate-700/30 sticky top-0 bg-gradient-to-r from-slate-900/95 to-slate-800/95 backdrop-blur-xl z-20 shadow-lg">
        <Link
          to="/"
          className="group p-3 rounded-full hover:bg-slate-700/50 transition-all duration-300 hover:scale-110 active:scale-95"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-slate-300 group-hover:text-white transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </Link>

        {/* Avatar with glow effect */}
        <div className="relative ml-4">
          <div className={`absolute inset-0 bg-gradient-to-r ${gender === 'female' ? 'from-pink-400 to-purple-400' : 'from-blue-400 to-cyan-400'} rounded-full blur-md transition-all duration-500 ${isLoading ? 'scale-110 opacity-60' : 'scale-100 opacity-30'}`}></div>
          <img
            src={persona.avatar}
            alt={persona.name}
            className={`relative w-12 h-12 rounded-full object-cover border-2 transition-all duration-300 ${
              isLoading
                ? `border-${gender === 'female' ? 'pink' : 'cyan'}-400 shadow-lg`
                : 'border-slate-600'
            }`}
          />
          {/* Online indicator */}
          <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-slate-900 transition-all duration-300 ${
            isLoading ? 'bg-yellow-400 animate-pulse' : 'bg-green-400'
          }`}></div>
        </div>

        <div className="ml-4 flex-1">
          <h2 className={`font-bold text-xl bg-clip-text text-transparent bg-gradient-to-r ${gender === 'female' ? 'from-pink-300 to-purple-300' : 'from-blue-300 to-cyan-300'}`}>
            {persona.name}
          </h2>
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full transition-all duration-300 ${
              isLoading ? 'bg-yellow-400 animate-pulse' : 'bg-green-400'
            }`}></div>
            <p className={`text-sm font-medium transition-all duration-300 ${
              isLoading ? 'text-yellow-300' : 'text-green-300'
            }`}>
              {isLoading ? (
                <span className="flex items-center gap-1">
                  typing
                  <span className="flex gap-1">
                    <span className="w-1 h-1 bg-current rounded-full animate-bounce" style={{animationDelay: '0ms'}}></span>
                    <span className="w-1 h-1 bg-current rounded-full animate-bounce" style={{animationDelay: '150ms'}}></span>
                    <span className="w-1 h-1 bg-current rounded-full animate-bounce" style={{animationDelay: '300ms'}}></span>
                  </span>
                </span>
              ) : 'Online'}
            </p>
          </div>
        </div>

        {/* Message count badge */}
        {messageCount > 0 && (
          <div className={`px-3 py-1 rounded-full text-xs font-bold bg-gradient-to-r ${gender === 'female' ? 'from-pink-500 to-purple-500' : 'from-blue-500 to-cyan-500'} text-white shadow-lg`}>
            {messageCount} messages
          </div>
        )}
      </header>

      <main className="flex-1 overflow-y-auto p-4 sm:p-6 space-y-6">
        {chatHistory.map((msg, index) => (
          <div key={index} className={`flex gap-3 ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
            {msg.role === 'model' && <img src={persona.avatar} alt="bot" className="w-8 h-8 rounded-full self-end object-cover" />}
            <div className={`py-2 px-4 rounded-xl max-w-sm md:max-w-md ${msg.role === 'user' ? 'bg-cyan-500 text-white rounded-br-none' : 'bg-slate-700 text-slate-200 rounded-bl-none'}`}>
              <p className="whitespace-pre-wrap">{msg.parts[0].text}</p>
              <p className="text-xs text-right mt-1 opacity-60">{msg.timestamp}</p>
            </div>
          </div>
        ))}
        <div ref={chatEndRef} />
      </main>

      <footer className="p-3 sm:p-4 border-t border-slate-700/50 sticky bottom-0 bg-slate-900/70 backdrop-blur-sm">
        <form onSubmit={sendMessage} className="flex items-center gap-2 sm:gap-4">
          <input
            type="text" value={userInput} onChange={(e) => setUserInput(e.target.value)}
            placeholder="Type your message..."
            className="flex-1 bg-slate-700 rounded-full py-3 px-5 outline-none focus:ring-2 focus:ring-cyan-500 transition-all text-white"
            disabled={isLoading}
          />
          <button type="submit" disabled={isLoading || !userInput.trim()} className="bg-cyan-500 rounded-full p-3 text-white transition-all hover:scale-110 active:scale-95 disabled:bg-slate-600 disabled:hover:scale-100 disabled:cursor-not-allowed">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor"><path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" /></svg>
          </button>
        </form>
      </footer>
    </div>
  );
};

export default ChatPage;