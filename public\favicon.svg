<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ec4899;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="50" cy="50" r="45" fill="url(#grad1)" />
  
  <!-- Chat Bubble -->
  <path d="M25 35 C25 30, 30 25, 35 25 L65 25 C70 25, 75 30, 75 35 L75 50 C75 55, 70 60, 65 60 L45 60 L35 70 L35 60 C30 60, 25 55, 25 50 Z" fill="white" opacity="0.9"/>
  
  <!-- Chat Dots -->
  <circle cx="40" cy="42.5" r="3" fill="url(#grad1)" />
  <circle cx="50" cy="42.5" r="3" fill="url(#grad1)" />
  <circle cx="60" cy="42.5" r="3" fill="url(#grad1)" />
  
  <!-- Heart accent -->
  <path d="M60 20 C62 18, 65 18, 67 20 C69 18, 72 18, 74 20 C76 22, 76 25, 74 27 L67 34 L60 27 C58 25, 58 22, 60 20 Z" fill="white" opacity="0.8"/>
</svg>
